import re
from datetime import datetime
from typing import Optional, Union
from flask import g
from langchain_core.tools import tool

from utils.utils import extract_value

@tool
def check_future_irrigation_fields(calculation_date: Optional[Union[datetime, str]] = None) -> str:
    """
    Identify which fields will need irrigation on a specific future date to help with advance planning.

    USE THIS TOOL WHEN:
    - The user asks which fields will need irrigation on a specific future date
    - The user wants to plan irrigation for a particular day in advance
    - The user asks if they need to irrigate on a specific future date
    - The user wants to know irrigation requirements for a date other than today

    DO NOT USE THIS TOOL WHEN:
    - The user asks about today's irrigation needs (use check_today_active_irrigation_user)
    - The user asks about irrigation for a range of dates (use check_irrigation_needs_between_period)
    - The user asks about a specific field without mentioning a date (use field-specific tools)
    - The user asks about soil moisture rather than irrigation (use soil moisture tools)

    EXAMPLE QUERIES:
    - "Which fields will need irrigation on May 15th?"
    - "Do I need to irrigate any fields next Monday?"
    - "What are the irrigation requirements for my fields on June 1st?"
    - "Will any of my fields need water on the 20th of this month?"

    Args:
        calculation_date (datetime or str, optional): The future date to check for irrigation needs,
                                                    formatted as 'YYYY-MM-DD'. Must be a date after today.

    Returns:
        str: Detailed information about the fields needing irrigation on the specified date,
             including field names and irrigation volumes, or an error message if the date is
             invalid or data retrieval fails.
    """
    try:

        calculation_date =   extract_value(calculation_date, "calculation_date")

        if isinstance(calculation_date, datetime):
            calculation_date = calculation_date.strftime('%Y-%m-%d')

        response = g.seabex_api.tools().irrigations().call_tool(
            'check_earliest_irrigation_dates',
            {
                'calculation_date': calculation_date
            }
        )


        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"


        if response:
            return response
        else:
            return "No fields need irrigation on the specified date."

    except Exception as e:
        print(e)
        return (
            "I'm sorry, I couldn't retrieve the irrigation dates at the moment. "
            "Please try again later or provide more details."
        )
