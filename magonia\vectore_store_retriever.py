import os
from langchain_openai import OpenAIEmbeddings
from langchain_redis import RedisConfig, RedisVectorStore


class VectorStoreRetriever:
    def __init__(self, redis_url, index_name):
        if not redis_url:
            raise ValueError("The REDIS_URL environment variable is not set or is invalid.")

        self.embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
        self.config = RedisConfig(
            index_name=index_name,
            redis_url=redis_url,
            metadata_schema=[{"name": "category", "type": "tag"}],
        )
        self.vector_store = RedisVectorStore(self.embeddings, config=self.config)

    def load_and_embed(self, file_path):
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Split the content by the delimiter
        entries = content.split("*********")
        texts = []

        for entry in entries:
            entry = entry.strip()
            if entry:
                parts = entry.split("Réponse:")
                if len(parts) == 2:
                    question = parts[0].replace("Question:", "").strip()
                    answer = parts[1].strip()
                    texts.append(f"{question} {answer}")

        if not texts:
            raise ValueError("No valid question-answer pairs found in the file.")

        metadata = [{"category": "general"}] * len(texts)
        ids = self.vector_store.add_texts(texts, metadata)
        return ids

    def lookup_document(self, query, k=2):
        results = self.vector_store.similarity_search(query, k=k)
        return results

