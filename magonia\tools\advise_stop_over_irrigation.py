from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_input

@tool
def advise_stop_over_irrigation(field_name: Optional[str] = None ) -> str:
    """
    Advise when to stop irrigating the specified field to avoid over-irrigation.

    Args:
        field_name (str): The name of the field.

    Returns:
        str: A message indicating when to stop irrigating or an error message.
    """
    try:
        # Check if field_name is a JSON-like string that needs parsing
        if field_name and ('{' in field_name or '=' in field_name or ':' in field_name):
            parsed_data = parse_input(field_name)
            print("Parsed data:", parsed_data)
            field_name = str(parsed_data.get('field_name', '')).strip().replace('"', '').replace("'", '')
        else:
            # If it's just a plain field name, clean it
            field_name = str(field_name).strip().replace('"', '').replace("'", '')

        response = g.seabex_api.tools().irrigations().call_tool(
            'advise_stop_irrigation',
            {'field_name': field_name}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"

        if not response:
            return f"Good news! Field '{field_name}' is not at risk of over-irrigation right now. Your current irrigation levels are appropriate."

        return response


    except Exception as e:
        print(f"Error advising on over-irrigation: {e}")
        return (
            "I'm sorry, I couldn't provide advice on over-irrigation at the moment. "
            "Please try again later."
        )
