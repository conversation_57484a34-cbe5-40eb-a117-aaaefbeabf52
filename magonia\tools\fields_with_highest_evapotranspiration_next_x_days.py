from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def fields_with_highest_evapotranspiration_next_x_days(days: Optional[int] = 5, user_id: Optional[str] = None) -> dict:
    """
    Identify fields with the highest predicted evapotranspiration rates over the next X days to help with water management.

    USE THIS TOOL WHEN:
    - The user asks which fields will have the highest water loss
    - The user wants to know about evapotranspiration rates across fields
    - The user needs to identify fields with high water stress potential
    - The user asks about water consumption by plants in different fields

    DO NOT USE THIS TOOL WHEN:
    - The user asks about irrigation requirements (use irrigation-specific tools)
    - The user asks about a specific field without comparing to others
    - The user asks about soil moisture rather than evapotranspiration
    - The user asks about historical rather than future evapotranspiration

    EXAMPLE QUERIES:
    - "Which fields will have the highest evapotranspiration in the next 5 days?"
    - "Where will water loss be greatest in my fields this week?"
    - "Which crops are likely to lose the most water through evapotranspiration?"
    - "Identify fields with highest evapotranspiration rates for the coming days"

    Args:
        days (int, optional): The number of future days to check for evapotranspiration.
                             Defaults to 5. Must be a positive integer.
        user_id (str, optional): The ID of the user making the request.
                                This is used for authentication and data retrieval.

    Returns:
        dict: A ranked list of fields with the highest evapotranspiration rates,
              including field names and evapotranspiration values, or an error message
              if data retrieval fails.
    """
    try:
        # Only parse if days is a string
        if isinstance(days, str):
            days = parse_integer_from_string(days)

        # Ensure days is an integer
        if not isinstance(days, int):
            days = 5  # Default to 5 days if not a valid integer

        # Prepare payload with days and user_id if provided
        payload = {'days': days}
        if user_id:
            payload['user_id'] = user_id

        response = g.seabex_api.tools().irrigations().call_tool(
            'fields_with_highest_evapotranspiration_next_x_days',
            payload
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        # Handle empty data case
        if isinstance(response, dict) and 'data' in response and not response['data']:
            return {
                "message": f"No fields show significant water loss through evapotranspiration for the next {days} days. This is good news - your crops are likely well-hydrated and weather conditions are favorable for water retention."
            }

        return response

    except Exception as e:
        print(f"Error retrieving evapotranspiration data: {e}")
        return {
            "message": "I'm sorry, I couldn't retrieve the evapotranspiration information at the moment. Please try again later."
        }
