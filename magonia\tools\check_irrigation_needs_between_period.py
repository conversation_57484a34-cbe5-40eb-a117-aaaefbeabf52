from typing import Optional
from flask import g
from langchain_core.tools import tool
from utils.utils import parse_input
from datetime import datetime

@tool
def check_irrigation_needs_between_period(
    field_name: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
) -> str:
    """
    Retrieve a detailed list of irrigation recommendations for a specified field within a given date range.

    USE THIS TOOL WHEN:
    - The user asks about irrigation for a specific field during a specific date range
    - The user wants to know irrigation needs between two specific dates
    - The user asks if they need to irrigate a named field during a specific period
    - The user wants irrigation volumes for a field between two dates

    DO NOT USE THIS TOOL WHEN:
    - The user asks only about today's irrigation (use check_today_active_irrigation_user)
    - The user asks about irrigation for the next X days without specifying dates (use check_irrigation_need_for_x_days)
    - The user doesn't specify a field name (use more general irrigation tools)
    - The user doesn't provide a date range (use other irrigation tools)

    EXAMPLE QUERIES:
    - "Do I need to irrigate my field ChleWi between May 10 and May 22?"
    - "What are the irrigation recommendations for my olive grove from June 1 to June 15?"
    - "How much water will my tomato field need between next Monday and Friday?"
    - "Check irrigation needs for Field1 from tomorrow until next week"

    Args:
        field_name (str, optional): The name of the field to check. Must not be empty.
        from_date (str, optional): The start date of the assessment period
                                 in YYYY-MM-DD format. Must be a valid date.
        to_date (str, optional): The end date of the assessment period
                               in YYYY-MM-DD format. Must be a valid date and after from_date.

    Returns:
        str: A detailed list of irrigation recommendations including dates and volumes if data retrieval is successful,
             or a message indicating issues with data retrieval or input validation.
    """
    if not field_name:
        return "Field name must not be empty."

    try:
        # Check if field_name is a JSON-like string that needs parsing
        if field_name and ('{' in field_name or '=' in field_name or ':' in field_name):
            # Parse the tool input
            parsed_data = parse_input(field_name)
            print("Parsed data:", parsed_data)

            # Extract the values from the parsed data
            field_name = str(parsed_data.get('field_name', '')).strip().replace('"', '').replace("'", '')
            from_date = str(parsed_data.get('from_date', '')).strip().replace('"', '').replace("'", '')
            to_date = str(parsed_data.get('to_date', '')).strip().replace('"', '').replace("'", '')

        # If from_date and to_date are provided as separate parameters, use them
        if not from_date and from_date is not None:
            from_date = str(from_date).strip().replace('"', '').replace("'", '')
        if not to_date and to_date is not None:
            to_date = str(to_date).strip().replace('"', '').replace("'", '')

        # Validate dates
        if not (from_date and to_date):
            return "Both from_date and to_date must be provided."



        response = g.seabex_api.tools().irrigations().call_tool(
            'check_irrigation_needs_between_period',
            {
                'field_name':field_name,
                'from_date':from_date,
                'to_date':to_date
            }
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"

        if response and response.get("count", 0) > 0:
            area_name = response.get("area_name", "Unknown Area")
            total_volume = float(response.get("total_irrigation_volume", 0))
            irrigations = response.get("irrigations", [])

            # Filter out entries with zero volume
            irrigations_needed = [
                irrigation for irrigation in irrigations
                if float(irrigation.get('irrigation_volume', 0)) > 0
            ]

            if irrigations_needed:
                # Build irrigation details with bullet points
                irrigation_details = [
                    f"• {irrigation['calculation_date']}: {float(irrigation['irrigation_volume']):.2f} mm"
                    for irrigation in irrigations_needed
                ]

                irrigation_summary = "\n".join(irrigation_details)
                days_count = len(irrigations_needed)

                result = (
                    f"Irrigation recommendations for {area_name} between {from_date} and {to_date}:\n\n"
                    f"{irrigation_summary}\n\n"
                    f"Total irrigation volume needed: {total_volume:.2f} mm over {days_count} day(s)."
                )

                # Add a summary if there are multiple days
                if days_count > 1:
                    result += f"\nAverage daily irrigation: {(total_volume/days_count):.2f} mm."

                return result
            else:
                return (
                    f"No irrigation is needed for {area_name} between {from_date} and {to_date}.\n\n"
                    f"The soil moisture levels are sufficient for this period based on current forecasts."
                )
        else:
            return (
                f"No irrigation is needed for field '{field_name}' between {from_date} and {to_date}.\n\n"
                f"This could be due to sufficient soil moisture levels during this period."
            )

    except Exception as e:
        print(f"Error retrieving irrigation data: {e}")
        return (
            "I'm sorry, I couldn't retrieve the irrigation recommendations at the moment. "
            "Please try again later."
        )
