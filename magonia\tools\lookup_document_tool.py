import os
from langchain_core.tools import tool
from magonia.vectore_store_retriever import VectorStoreRetriever

# Load Redis URL
redis_url = os.getenv("REDIS_URL")

# Create separate retrievers for each document collection
general_retriever = VectorStoreRetriever(redis_url, "general_questions")
tunisian_retriever = VectorStoreRetriever(redis_url, "QuestionTunisian")

# Load and embed the documents from multiple text files
try:
    # Load and embed both 'general_questions.txt' and 'QuestionTunisian.txt'
    general_retriever.load_and_embed("general_questions.txt")
    tunisian_retriever.load_and_embed("QuestionTunisian.txt")
    print(f"Successfully loaded and embedded both document collections")
except Exception as e:
    print(f"Error loading embeddings: {str(e)}")

@tool
def lookup_document_tool(query: str) -> str:
    """
    Search through pre-stored Q&A documents to find SPECIFIC, FACTUAL answers that require domain expertise.

    IMPORTANT: For general agricultural knowledge questions, DO NOT use this tool.
    Instead, use your built-in knowledge to answer directly.

    USE THIS TOOL WHEN (AND ONLY WHEN):
    - The user asks about SPECIFIC technical details that might be in documentation
    - The user needs PRECISE factual information about Seabex products or services
    - The user asks about EXACT specifications or procedures that require reference material
    - The user explicitly asks for information from documentation or manuals

    DO NOT USE THIS TOOL WHEN:
    - The user asks general questions about agriculture or irrigation (use built-in knowledge)
    - The user asks about best practices in farming (use built-in knowledge)
    - The user asks educational questions like "How does drip irrigation work?" (use built-in knowledge)
    - The user asks about their specific fields or data (use field-specific tools)
    - The user asks about current conditions or recommendations (use real-time tools)
    - The user asks about personal preferences or memories (use memory tools)
    - The user asks about dates or times (use time tools)

    EXAMPLE QUERIES THAT SHOULD NOT USE THIS TOOL:
    - "What is the best irrigation method for olive trees?" (use built-in knowledge)
    - "How can I improve soil moisture retention?" (use built-in knowledge)
    - "What are the signs of over-irrigation?" (use built-in knowledge)
    - "How does drip irrigation work?" (use built-in knowledge)

    Args:
        query (str): The user's question or search query.

    Returns:
        str: Relevant information from the knowledge base that answers the query,
             or a message indicating no relevant information was found.
    """
    try:
        # Search both document collections
        general_results = general_retriever.lookup_document(query, k=1)
        tunisian_results = tunisian_retriever.lookup_document(query, k=1)

        # Combine results from both retrievers
        all_results = []
        if general_results:
            all_results.extend(general_results)
        if tunisian_results:
            all_results.extend(tunisian_results)

        if all_results:
            # Return the best result (first one from combined results)
            best_result = all_results[0]
            return f"Answer: {best_result.page_content}"

        return "I couldn't find specific documentation that answers your question. However, I can help you with general agricultural knowledge or direct you to the right tools for your farming needs."
    except Exception as e:
        return f"An error occurred during document lookup: {str(e)}"
